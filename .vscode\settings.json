{
  "editor.defaultFormatter": "ms-python.black-formatter",
  "editor.formatOnSave": true,
  //"python.formatting.provider": "black",
  "python.defaultInterpreterPath": "E:\\XiGPrograms\\anaconda\\base\\envs\\sweep\\python.exe",
  //"python.analysis.typeCheckingMode": "strict",
  "python.analysis.packageIndexDepths": [
    

    {
      "name": "sklearn",
      "depth": 2
    },
    {
      "name": "matplotlib",
      "depth": 2
    },
    {
      "name": "scipy",
      "depth": 2
    },
    {
      "name": "django",
      "depth": 2
    },
    {
      "name": "flask",
      "depth": 2
    },
    {
      "name": "fastapi",
      "depth": 2
    },
    {
      "name": "cuda",
      "depth": 3,
      "includeAllSymbols": true
    },
    {
      "name": "nidaqmx",
      "depth": 3,
      "includeAllSymbols": false
    },
    {
      "name": "sweeper400",
      "depth": 3,
      "includeAllSymbols": false
    }
  ],
  //"python.analysis.typeCheckingMode": "strict",
  "python-envs.pythonProjects": [
    {
      "path": "",
      "envManager": "ms-python.python:conda",
      "packageManager": "ms-python.python:conda"
    }
  ]
}