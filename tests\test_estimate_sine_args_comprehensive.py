"""
estimate_sine_args 函数的全面测试

验证粗略参数估计函数在各种情况下的准确性，特别关注：
1. 幅值估计的准确性
2. 频率估计的准确性  
3. 相位估计的准确性
4. 边界条件和特殊情况
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
    estimate_sine_args,
)


def test_basic_accuracy():
    """测试基本参数估计准确性"""
    print("=== 基本准确性测试 ===")
    
    # 测试参数组合
    test_cases = [
        {"freq": 50.0, "amp": 1.0, "phase": 0.0, "name": "标准情况"},
        {"freq": 50.0, "amp": 1.414, "phase": 0.0, "name": "你提到的问题案例"},
        {"freq": 100.0, "amp": 2.0, "phase": np.pi/2, "name": "高频+大幅值"},
        {"freq": 10.0, "amp": 0.5, "phase": -np.pi/2, "name": "低频+小幅值"},
        {"freq": 200.0, "amp": 3.0, "phase": np.pi, "name": "更高频率"},
    ]
    
    sampling_info = init_sampling_info(1000, 2000)  # 1kHz, 2000点
    
    for i, case in enumerate(test_cases):
        print(f"\n--- 测试 {i+1}: {case['name']} ---")
        
        # 生成测试信号
        true_args = init_sine_args(case["freq"], case["amp"], case["phase"])
        test_wave = get_sine(sampling_info, true_args)
        
        print(f"真实参数: 频率={case['freq']}Hz, 幅值={case['amp']}, 相位={case['phase']:.3f}rad ({np.rad2deg(case['phase']):.1f}°)")
        
        try:
            # 估计参数
            estimated_args = estimate_sine_args(test_wave, approx_freq=case["freq"])
            
            # 计算误差
            freq_error = abs(estimated_args["frequency"] - case["freq"])
            amp_error = abs(estimated_args["amplitude"] - case["amp"])
            
            # 相位误差（考虑周期性）
            phase_diff = estimated_args["phase"] - case["phase"]
            phase_error = abs(np.arctan2(np.sin(phase_diff), np.cos(phase_diff)))
            
            print(f"估计参数: 频率={estimated_args['frequency']:.6f}Hz, 幅值={estimated_args['amplitude']:.6f}, 相位={estimated_args['phase']:.6f}rad ({np.rad2deg(estimated_args['phase']):.1f}°)")
            print(f"绝对误差: 频率={freq_error:.6f}Hz, 幅值={amp_error:.6f}, 相位={phase_error:.6f}rad ({np.rad2deg(phase_error):.3f}°)")
            
            # 相对误差
            freq_rel_error = freq_error / case["freq"] * 100
            amp_rel_error = amp_error / case["amp"] * 100
            
            print(f"相对误差: 频率={freq_rel_error:.4f}%, 幅值={amp_rel_error:.4f}%")
            
            # 检查是否在可接受范围内
            if freq_error > 0.1:
                print("⚠️  频率误差较大")
            if amp_error > 0.1:
                print("⚠️  幅值误差较大")
            if phase_error > np.deg2rad(5):
                print("⚠️  相位误差较大")
            
            if freq_error <= 0.1 and amp_error <= 0.1 and phase_error <= np.deg2rad(5):
                print("✓  所有参数估计正常")
                
        except Exception as e:
            print(f"❌ 估计失败: {e}")


def test_amplitude_scaling():
    """专门测试幅值估计的准确性"""
    print("\n=== 幅值估计专项测试 ===")
    
    sampling_info = init_sampling_info(1000, 2000)
    frequency = 50.0
    phase = 0.0
    
    # 测试不同的幅值
    test_amplitudes = [0.1, 0.5, 1.0, 1.414, 2.0, 3.0, 5.0]
    
    print(f"固定参数: 频率={frequency}Hz, 相位={phase}rad")
    print("测试不同幅值的估计准确性:")
    print()
    
    for amp in test_amplitudes:
        print(f"--- 真实幅值: {amp} ---")
        
        # 生成信号
        true_args = init_sine_args(frequency, amp, phase)
        test_wave = get_sine(sampling_info, true_args)
        
        try:
            estimated_args = estimate_sine_args(test_wave, approx_freq=frequency)
            estimated_amp = estimated_args["amplitude"]
            
            amp_error = abs(estimated_amp - amp)
            amp_rel_error = amp_error / amp * 100
            
            print(f"估计幅值: {estimated_amp:.6f}")
            print(f"绝对误差: {amp_error:.6f}")
            print(f"相对误差: {amp_rel_error:.3f}%")
            
            if amp_rel_error > 5.0:  # 相对误差超过5%
                print("⚠️  幅值估计误差较大")
            else:
                print("✓  幅值估计正常")
                
        except Exception as e:
            print(f"❌ 估计失败: {e}")
        
        print()


def test_phase_boundaries():
    """测试相位边界情况"""
    print("=== 相位边界测试 ===")
    
    sampling_info = init_sampling_info(1000, 3000)
    frequency = 50.0
    amplitude = 1.5
    
    # 测试边界相位
    test_phases = [
        -np.pi, -3*np.pi/4, -np.pi/2, -np.pi/4, 0.0,
        np.pi/4, np.pi/2, 3*np.pi/4, np.pi
    ]
    
    print(f"固定参数: 频率={frequency}Hz, 幅值={amplitude}")
    print("测试边界相位的估计准确性:")
    print()
    
    for phase in test_phases:
        print(f"--- 真实相位: {phase:.3f}rad ({np.rad2deg(phase):.1f}°) ---")
        
        try:
            true_args = init_sine_args(frequency, amplitude, phase)
            test_wave = get_sine(sampling_info, true_args)
            
            estimated_args = estimate_sine_args(test_wave, approx_freq=frequency)
            estimated_phase = estimated_args["phase"]
            
            # 相位误差（考虑周期性）
            phase_diff = estimated_phase - phase
            phase_error = abs(np.arctan2(np.sin(phase_diff), np.cos(phase_diff)))
            
            print(f"估计相位: {estimated_phase:.6f}rad ({np.rad2deg(estimated_phase):.1f}°)")
            print(f"相位误差: {phase_error:.6f}rad ({np.rad2deg(phase_error):.3f}°)")
            
            if phase_error > np.deg2rad(2):
                print("⚠️  相位估计误差较大")
            else:
                print("✓  相位估计正常")
                
        except Exception as e:
            print(f"❌ 估计失败: {e}")
        
        print()


def test_frequency_range():
    """测试不同频率范围"""
    print("=== 频率范围测试 ===")
    
    amplitude = 1.0
    phase = 0.0
    
    # 测试不同频率和对应的采样设置
    test_cases = [
        {"freq": 5.0, "fs": 100, "samples": 1000, "name": "低频"},
        {"freq": 50.0, "fs": 1000, "samples": 2000, "name": "中频"},
        {"freq": 500.0, "fs": 5000, "samples": 5000, "name": "高频"},
        {"freq": 1000.0, "fs": 10000, "samples": 10000, "name": "更高频"},
    ]
    
    print(f"固定参数: 幅值={amplitude}, 相位={phase}rad")
    print("测试不同频率范围的估计准确性:")
    print()
    
    for case in test_cases:
        print(f"--- {case['name']}: {case['freq']}Hz (采样率{case['fs']}Hz) ---")
        
        sampling_info = init_sampling_info(case["fs"], case["samples"])
        
        try:
            true_args = init_sine_args(case["freq"], amplitude, phase)
            test_wave = get_sine(sampling_info, true_args)
            
            estimated_args = estimate_sine_args(test_wave, approx_freq=case["freq"])
            
            freq_error = abs(estimated_args["frequency"] - case["freq"])
            amp_error = abs(estimated_args["amplitude"] - amplitude)
            
            freq_rel_error = freq_error / case["freq"] * 100
            amp_rel_error = amp_error / amplitude * 100
            
            print(f"频率估计: {estimated_args['frequency']:.6f}Hz (误差{freq_error:.6f}Hz, {freq_rel_error:.4f}%)")
            print(f"幅值估计: {estimated_args['amplitude']:.6f} (误差{amp_error:.6f}, {amp_rel_error:.3f}%)")
            
            if freq_rel_error > 1.0 or amp_rel_error > 5.0:
                print("⚠️  估计误差较大")
            else:
                print("✓  估计正常")
                
        except Exception as e:
            print(f"❌ 估计失败: {e}")
        
        print()


def test_edge_cases():
    """测试边界条件和特殊情况"""
    print("=== 边界条件测试 ===")
    
    # 测试1: 很短的信号
    print("--- 短信号测试 ---")
    try:
        sampling_info = init_sampling_info(1000, 100)  # 只有100个点
        true_args = init_sine_args(50.0, 1.0, 0.5)
        test_wave = get_sine(sampling_info, true_args)
        estimated_args = estimate_sine_args(test_wave, approx_freq=50.0)
        print(f"短信号估计成功: {estimated_args}")
    except Exception as e:
        print(f"短信号估计失败: {e}")
    
    # 测试2: 很长的信号
    print("\n--- 长信号测试 ---")
    try:
        sampling_info = init_sampling_info(1000, 20000)  # 20000个点
        true_args = init_sine_args(50.0, 1.0, 0.5)
        test_wave = get_sine(sampling_info, true_args)
        estimated_args = estimate_sine_args(test_wave, approx_freq=50.0)
        print(f"长信号估计成功: {estimated_args}")
    except Exception as e:
        print(f"长信号估计失败: {e}")
    
    # 测试3: 极小幅值
    print("\n--- 极小幅值测试 ---")
    try:
        sampling_info = init_sampling_info(1000, 2000)
        true_args = init_sine_args(50.0, 0.01, 0.0)  # 很小的幅值
        test_wave = get_sine(sampling_info, true_args)
        estimated_args = estimate_sine_args(test_wave, approx_freq=50.0)
        print(f"极小幅值估计成功: {estimated_args}")
    except Exception as e:
        print(f"极小幅值估计失败: {e}")
    
    # 测试4: 极大幅值
    print("\n--- 极大幅值测试 ---")
    try:
        sampling_info = init_sampling_info(1000, 2000)
        true_args = init_sine_args(50.0, 100.0, 0.0)  # 很大的幅值
        test_wave = get_sine(sampling_info, true_args)
        estimated_args = estimate_sine_args(test_wave, approx_freq=50.0)
        print(f"极大幅值估计成功: {estimated_args}")
    except Exception as e:
        print(f"极大幅值估计失败: {e}")


if __name__ == "__main__":
    print("开始 estimate_sine_args 函数全面测试")
    print("=" * 60)
    
    try:
        test_basic_accuracy()
        test_amplitude_scaling()
        test_phase_boundaries()
        test_frequency_range()
        test_edge_cases()
        
        print("\n🎉 estimate_sine_args 全面测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
