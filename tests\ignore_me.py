# pyright: basic
import numpy as np
from sweeper400.analyze import (
    SamplingInfo,
    init_sampling_info,
    SineArgs,
    init_sine_args,
    get_sine,
    estimate_sine_args,
    extract_single_tone_information_vvi,
    Waveform,
)

sampling_info = init_sampling_info(68600, 34300)

sine_args = init_sine_args(3430.8, 1.414, 1.73)
testwave = get_sine(
    sampling_info,
    sine_args,
)

output1 = estimate_sine_args(testwave, approx_freq=3430.0)
output2 = extract_single_tone_information_vvi(testwave, approx_freq=3430.0)
print(output1)
print(output2)
