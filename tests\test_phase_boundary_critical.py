"""
Critical phase boundary testing for extract_single_tone_information_vvi

专门测试相位检测在边界附近的稳定性，进行高密度采样测试
重点关注 ±π 边界附近几度范围内的检测准确性
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
    extract_single_tone_information_vvi,
)


def test_critical_phase_boundaries():
    """在相位边界附近进行高密度测试"""
    print("=== 相位边界临界区域高密度测试 ===")
    
    # 测试参数
    sampling_info = init_sampling_info(1000, 3000)  # 1kHz采样率，3000个点
    frequency = 50.0
    amplitude = 1.5
    
    # 定义临界测试区域（边界附近各5度）
    boundary_range_deg = 5.0  # 边界附近±5度
    step_deg = 0.2  # 每0.2度测试一次
    
    critical_regions = [
        # 负π边界附近
        {
            "name": "负π边界附近",
            "center": -np.pi,
            "range_rad": np.deg2rad(boundary_range_deg)
        },
        # 正π边界附近  
        {
            "name": "正π边界附近",
            "center": np.pi,
            "range_rad": np.deg2rad(boundary_range_deg)
        },
        # 零点附近
        {
            "name": "零点附近",
            "center": 0.0,
            "range_rad": np.deg2rad(boundary_range_deg)
        },
        # ±π/2附近
        {
            "name": "+π/2附近",
            "center": np.pi/2,
            "range_rad": np.deg2rad(boundary_range_deg)
        },
        {
            "name": "-π/2附近", 
            "center": -np.pi/2,
            "range_rad": np.deg2rad(boundary_range_deg)
        }
    ]
    
    all_results = []
    
    for region in critical_regions:
        print(f"\n--- {region['name']} ---")
        print(f"中心: {region['center']:.3f} rad ({np.rad2deg(region['center']):.1f}°)")
        print(f"测试范围: ±{np.rad2deg(region['range_rad']):.1f}°")
        
        # 生成测试相位序列
        start_phase = region['center'] - region['range_rad']
        end_phase = region['center'] + region['range_rad']
        
        # 确保相位在有效范围内
        if start_phase < -np.pi:
            start_phase = -np.pi
        if end_phase > np.pi:
            end_phase = np.pi
            
        phase_step = np.deg2rad(step_deg)
        test_phases = np.arange(start_phase, end_phase + phase_step/2, phase_step)
        
        print(f"测试{len(test_phases)}个相位点，步长{step_deg:.1f}°")
        
        region_results = []
        failed_count = 0
        large_error_count = 0
        
        for i, phase in enumerate(test_phases):
            try:
                # 生成测试信号
                sine_args = init_sine_args(frequency, amplitude, phase)
                test_wave = get_sine(sampling_info, sine_args)
                
                # 提取信号信息
                detected_args = extract_single_tone_information_vvi(test_wave, approx_freq=frequency)
                
                # 计算相位误差（考虑周期性）
                phase_diff = detected_args["phase"] - phase
                phase_error = np.abs(np.arctan2(np.sin(phase_diff), np.cos(phase_diff)))
                
                # 计算其他误差
                freq_error = abs(detected_args["frequency"] - frequency)
                amp_error = abs(detected_args["amplitude"] - amplitude)
                
                result = {
                    "region": region["name"],
                    "original_phase_rad": phase,
                    "original_phase_deg": np.rad2deg(phase),
                    "detected_phase_rad": detected_args["phase"],
                    "detected_phase_deg": np.rad2deg(detected_args["phase"]),
                    "phase_error_rad": phase_error,
                    "phase_error_deg": np.rad2deg(phase_error),
                    "freq_error": freq_error,
                    "amp_error": amp_error,
                    "success": True
                }
                
                region_results.append(result)
                
                # 检查是否有大误差
                if phase_error > np.deg2rad(2.0):  # 相位误差超过2度
                    large_error_count += 1
                    print(f"  ⚠️  第{i+1:2d}点 {np.rad2deg(phase):6.1f}° → {np.rad2deg(detected_args['phase']):6.1f}° (误差{np.rad2deg(phase_error):5.2f}°)")
                elif i % 10 == 0:  # 每10个点显示一次正常结果
                    print(f"  ✓   第{i+1:2d}点 {np.rad2deg(phase):6.1f}° → {np.rad2deg(detected_args['phase']):6.1f}° (误差{np.rad2deg(phase_error):5.2f}°)")
                    
            except Exception as e:
                failed_count += 1
                print(f"  ❌  第{i+1:2d}点 {np.rad2deg(phase):6.1f}° 检测失败: {e}")
                
                result = {
                    "region": region["name"],
                    "original_phase_rad": phase,
                    "original_phase_deg": np.rad2deg(phase),
                    "detected_phase_rad": np.nan,
                    "detected_phase_deg": np.nan,
                    "phase_error_rad": np.inf,
                    "phase_error_deg": np.inf,
                    "freq_error": np.inf,
                    "amp_error": np.inf,
                    "success": False,
                    "error": str(e)
                }
                region_results.append(result)
        
        # 区域统计
        successful_results = [r for r in region_results if r["success"]]
        success_rate = len(successful_results) / len(region_results) * 100
        
        print(f"\n{region['name']} 统计:")
        print(f"  成功率: {success_rate:.1f}% ({len(successful_results)}/{len(region_results)})")
        print(f"  失败数: {failed_count}")
        print(f"  大误差数: {large_error_count}")
        
        if successful_results:
            phase_errors = [r["phase_error_deg"] for r in successful_results]
            avg_error = np.mean(phase_errors)
            max_error = np.max(phase_errors)
            std_error = np.std(phase_errors)
            
            print(f"  平均相位误差: {avg_error:.3f}°")
            print(f"  最大相位误差: {max_error:.3f}°")
            print(f"  相位误差标准差: {std_error:.3f}°")
            
            # 找出最大误差的点
            max_error_idx = np.argmax(phase_errors)
            worst_result = successful_results[max_error_idx]
            print(f"  最大误差点: {worst_result['original_phase_deg']:.1f}° → {worst_result['detected_phase_deg']:.1f}°")
        
        all_results.extend(region_results)
    
    return all_results


def test_phase_wrap_around():
    """测试相位跨越±π边界的情况"""
    print("\n=== 相位跨越边界测试 ===")
    
    sampling_info = init_sampling_info(1000, 2000)
    frequency = 50.0
    amplitude = 1.0
    
    # 测试跨越边界的相位对
    boundary_test_pairs = [
        (-np.pi + 0.01, "接近-π"),
        (np.pi - 0.01, "接近+π"),
        (-np.pi, "精确-π"),
        (np.pi, "精确+π"),
    ]
    
    for phase, description in boundary_test_pairs:
        print(f"\n--- {description}: {phase:.3f} rad ({np.rad2deg(phase):.1f}°) ---")
        
        try:
            sine_args = init_sine_args(frequency, amplitude, phase)
            test_wave = get_sine(sampling_info, sine_args)
            detected_args = extract_single_tone_information_vvi(test_wave, approx_freq=frequency)
            
            # 计算相位误差
            phase_diff = detected_args["phase"] - phase
            phase_error = np.abs(np.arctan2(np.sin(phase_diff), np.cos(phase_diff)))
            
            print(f"原始相位: {phase:.6f} rad ({np.rad2deg(phase):.3f}°)")
            print(f"检测相位: {detected_args['phase']:.6f} rad ({np.rad2deg(detected_args['phase']):.3f}°)")
            print(f"相位误差: {phase_error:.6f} rad ({np.rad2deg(phase_error):.3f}°)")
            
            if phase_error > np.deg2rad(1.0):
                print("⚠️  相位误差较大!")
            else:
                print("✓  相位检测正常")
                
        except Exception as e:
            print(f"❌ 检测失败: {e}")


if __name__ == "__main__":
    print("开始相位边界临界区域测试")
    print("=" * 50)
    
    try:
        # 高密度边界测试
        results = test_critical_phase_boundaries()
        
        # 边界跨越测试
        test_phase_wrap_around()
        
        # 总体统计
        print("\n" + "=" * 50)
        print("=== 总体测试统计 ===")
        
        successful_results = [r for r in results if r["success"]]
        total_tests = len(results)
        success_count = len(successful_results)
        
        print(f"总测试数: {total_tests}")
        print(f"成功数: {success_count}")
        print(f"总成功率: {success_count/total_tests*100:.1f}%")
        
        if successful_results:
            all_phase_errors = [r["phase_error_deg"] for r in successful_results]
            print(f"全局平均相位误差: {np.mean(all_phase_errors):.3f}°")
            print(f"全局最大相位误差: {np.max(all_phase_errors):.3f}°")
            print(f"全局相位误差标准差: {np.std(all_phase_errors):.3f}°")
            
            # 统计大误差的数量
            large_errors = [e for e in all_phase_errors if e > 2.0]
            if large_errors:
                print(f"⚠️  大误差(>2°)数量: {len(large_errors)}")
                print(f"⚠️  大误差比例: {len(large_errors)/len(all_phase_errors)*100:.1f}%")
            else:
                print("✓  无大误差(>2°)")
        
        print("\n🎉 临界区域测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
