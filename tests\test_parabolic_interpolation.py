"""
测试抛物线插值的效果

验证当信号频率不在FFT网格点上时，抛物线插值是否能正确估计幅值
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
)


def test_off_grid_frequencies():
    """测试非网格频率的估计效果"""
    print("=== 非网格频率测试 ===")
    
    # 固定参数
    sampling_rate = 1000
    samples_num = 2000
    true_amplitude = 2.0
    phase = 0.0
    
    # 计算频率分辨率
    freq_resolution = sampling_rate / samples_num
    print(f"频率分辨率: {freq_resolution} Hz")
    
    # 测试不同的频率偏移
    base_frequency = 50.0  # 基础频率（在网格上）
    frequency_offsets = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]  # 相对于网格的偏移
    
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    for offset in frequency_offsets:
        test_frequency = base_frequency + offset
        print(f"\n--- 测试频率: {test_frequency} Hz (偏移 {offset} Hz) ---")
        
        # 生成信号
        sine_args = init_sine_args(test_frequency, true_amplitude, phase)
        test_wave = get_sine(sampling_info, sine_args)
        
        # 手动进行FFT分析（模拟estimate_sine_args的逻辑）
        waveform_data = test_wave if test_wave.ndim == 1 else test_wave[0, :]
        
        # FFT
        fft_result = np.fft.fft(waveform_data)
        fft_freqs = np.fft.fftfreq(samples_num, 1/sampling_rate)
        
        # 正频率部分
        positive_mask = fft_freqs > 0
        fft_freqs_positive = fft_freqs[positive_mask]
        fft_magnitude = np.abs(fft_result[positive_mask])
        
        # 找到峰值附近的区域
        target_idx = np.argmin(np.abs(fft_freqs_positive - test_frequency))
        peak_magnitude = fft_magnitude[target_idx]
        peak_frequency = fft_freqs_positive[target_idx]
        
        print(f"FFT峰值: 频率={peak_frequency:.3f}Hz, 幅度={peak_magnitude:.6f}")
        
        # 直接转换（无插值）
        direct_amplitude = peak_magnitude * 2.0 / samples_num
        direct_error = abs(direct_amplitude - true_amplitude)
        
        print(f"直接转换: 幅值={direct_amplitude:.6f}, 误差={direct_error:.6f} ({direct_error/true_amplitude*100:.2f}%)")
        
        # 抛物线插值（如果可能）
        if target_idx > 0 and target_idx < len(fft_magnitude) - 1:
            y1 = fft_magnitude[target_idx - 1]
            y2 = fft_magnitude[target_idx]
            y3 = fft_magnitude[target_idx + 1]
            
            # 频率插值
            if abs(y1 - 2 * y2 + y3) > 1e-10:
                delta = 0.5 * (y1 - y3) / (y1 - 2 * y2 + y3)
                interpolated_frequency = peak_frequency + delta * freq_resolution
                
                # 幅值插值（当前代码的方法）
                interpolated_magnitude = y2 - 0.25 * (y1 - y3) * delta
                interpolated_amplitude = interpolated_magnitude * 2.0 / samples_num
                
                # 改进的幅值插值方法
                # 使用抛物线的峰值
                improved_magnitude = y2 - (y1 - y3)**2 / (8 * (y1 - 2*y2 + y3))
                improved_amplitude = improved_magnitude * 2.0 / samples_num
                
                interp_error = abs(interpolated_amplitude - true_amplitude)
                improved_error = abs(improved_amplitude - true_amplitude)
                
                print(f"抛物线插值:")
                print(f"  频率: {interpolated_frequency:.6f}Hz (delta={delta:.3f})")
                print(f"  当前方法: 幅值={interpolated_amplitude:.6f}, 误差={interp_error:.6f} ({interp_error/true_amplitude*100:.2f}%)")
                print(f"  改进方法: 幅值={improved_amplitude:.6f}, 误差={improved_error:.6f} ({improved_error/true_amplitude*100:.2f}%)")
            else:
                print("抛物线插值: 分母接近零，无法插值")
        else:
            print("抛物线插值: 边界情况，无法插值")


def test_your_specific_case_interpolation():
    """专门测试你的案例的插值效果"""
    print("\n=== 你的案例插值测试 ===")
    
    # 你的参数
    sampling_info = init_sampling_info(68600, 34300)
    sine_args = init_sine_args(3430.8, 1.414, 1.73)
    
    print(f"测试参数: 频率={sine_args['frequency']}Hz, 幅值={sine_args['amplitude']}")
    
    # 生成信号
    test_wave = get_sine(sampling_info, sine_args)
    waveform_data = test_wave if test_wave.ndim == 1 else test_wave[0, :]
    
    samples_num = len(waveform_data)
    sampling_rate = sampling_info['sampling_rate']
    freq_resolution = sampling_rate / samples_num
    
    print(f"频率分辨率: {freq_resolution} Hz")
    
    # FFT分析
    fft_result = np.fft.fft(waveform_data)
    fft_freqs = np.fft.fftfreq(samples_num, 1/sampling_rate)
    
    positive_mask = fft_freqs > 0
    fft_freqs_positive = fft_freqs[positive_mask]
    fft_magnitude = np.abs(fft_result[positive_mask])
    
    # 找峰值
    target_freq = sine_args['frequency']
    target_idx = np.argmin(np.abs(fft_freqs_positive - target_freq))
    
    print(f"目标频率: {target_freq}Hz")
    print(f"最近网格频率: {fft_freqs_positive[target_idx]}Hz")
    print(f"频率偏差: {target_freq - fft_freqs_positive[target_idx]:.3f}Hz")
    
    # 检查峰值附近的幅度
    if target_idx > 0 and target_idx < len(fft_magnitude) - 1:
        y1 = fft_magnitude[target_idx - 1]
        y2 = fft_magnitude[target_idx]
        y3 = fft_magnitude[target_idx + 1]
        
        print(f"\n峰值附近的FFT幅度:")
        print(f"  左侧: {y1:.6f}")
        print(f"  中心: {y2:.6f}")
        print(f"  右侧: {y3:.6f}")
        
        # 直接转换
        direct_amp = y2 * 2.0 / samples_num
        print(f"\n直接转换: {direct_amp:.6f}")
        
        # 当前的抛物线插值
        if abs(y1 - 2 * y2 + y3) > 1e-10:
            delta = 0.5 * (y1 - y3) / (y1 - 2 * y2 + y3)
            current_interp_mag = y2 - 0.25 * (y1 - y3) * delta
            current_interp_amp = current_interp_mag * 2.0 / samples_num
            
            print(f"当前插值: delta={delta:.6f}, 幅值={current_interp_amp:.6f}")
            
            # 改进的插值方法
            # 方法1: 使用抛物线的真实峰值
            improved_mag = y2 - (y1 - y3)**2 / (8 * (y1 - 2*y2 + y3))
            improved_amp = improved_mag * 2.0 / samples_num
            
            print(f"改进插值: 幅值={improved_amp:.6f}")
            
            # 方法2: 考虑频谱泄漏的补偿
            # 当频率偏离网格时，需要补偿泄漏的能量
            leakage_factor = 1.0 / np.sinc(delta)  # sinc函数补偿
            compensated_amp = current_interp_amp * abs(leakage_factor)
            
            print(f"泄漏补偿: 因子={leakage_factor:.6f}, 幅值={compensated_amp:.6f}")
            
            print(f"\n与真实值{sine_args['amplitude']}的误差:")
            print(f"  直接转换: {abs(direct_amp - sine_args['amplitude']):.6f}")
            print(f"  当前插值: {abs(current_interp_amp - sine_args['amplitude']):.6f}")
            print(f"  改进插值: {abs(improved_amp - sine_args['amplitude']):.6f}")
            print(f"  泄漏补偿: {abs(compensated_amp - sine_args['amplitude']):.6f}")


if __name__ == "__main__":
    print("开始抛物线插值测试")
    print("=" * 50)
    
    test_off_grid_frequencies()
    test_your_specific_case_interpolation()
    
    print("\n🔍 抛物线插值测试完成！")
