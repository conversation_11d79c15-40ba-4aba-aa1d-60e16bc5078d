"""
FFT幅值估计调试测试

专门调试FFT幅值估计的问题，验证从FFT幅度谱到实际信号幅值的转换
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
)


def debug_fft_amplitude():
    """调试FFT幅值估计"""
    print("=== FFT幅值估计调试 ===")
    
    # 创建已知参数的信号
    sampling_rate = 1000
    samples_num = 2000
    frequency = 50.0
    true_amplitude = 1.414  # 你提到的问题案例
    phase = 0.0
    
    print(f"测试信号参数:")
    print(f"  采样率: {sampling_rate} Hz")
    print(f"  采样点数: {samples_num}")
    print(f"  频率: {frequency} Hz")
    print(f"  真实幅值: {true_amplitude}")
    print(f"  相位: {phase} rad")
    print()
    
    # 生成信号
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    sine_args = init_sine_args(frequency, true_amplitude, phase)
    test_wave = get_sine(sampling_info, sine_args)
    
    # 获取信号数据
    if test_wave.ndim == 2:
        waveform_data = test_wave[0, :]
    else:
        waveform_data = test_wave
    
    print(f"信号统计:")
    print(f"  最大值: {np.max(waveform_data):.6f}")
    print(f"  最小值: {np.min(waveform_data):.6f}")
    print(f"  峰峰值: {np.max(waveform_data) - np.min(waveform_data):.6f}")
    print(f"  RMS值: {np.sqrt(np.mean(waveform_data**2)):.6f}")
    print()
    
    # 进行FFT分析
    fft_result = np.fft.fft(waveform_data)
    fft_freqs = np.fft.fftfreq(samples_num, 1/sampling_rate)
    
    # 只取正频率部分
    positive_freq_mask = fft_freqs > 0
    fft_freqs_positive = fft_freqs[positive_freq_mask]
    fft_magnitude = np.abs(fft_result[positive_freq_mask])
    
    # 找到目标频率附近的峰值
    target_freq_idx = np.argmin(np.abs(fft_freqs_positive - frequency))
    peak_magnitude = fft_magnitude[target_freq_idx]
    peak_frequency = fft_freqs_positive[target_freq_idx]
    
    print(f"FFT分析结果:")
    print(f"  峰值频率: {peak_frequency:.6f} Hz")
    print(f"  FFT幅度谱值: {peak_magnitude:.6f}")
    print()
    
    # 测试不同的幅值转换方法
    print("不同幅值转换方法:")
    
    # 方法1: 直接乘以2（当前代码的方法）
    method1 = peak_magnitude * 2.0
    print(f"  方法1 (×2): {method1:.6f}")
    
    # 方法2: 考虑FFT的归一化
    method2 = peak_magnitude * 2.0 / samples_num
    print(f"  方法2 (×2/N): {method2:.6f}")
    
    # 方法3: 只考虑归一化
    method3 = peak_magnitude / samples_num
    print(f"  方法3 (/N): {method3:.6f}")
    
    # 方法4: 考虑单边谱的正确转换
    method4 = peak_magnitude * 2.0 / samples_num * 2.0
    print(f"  方法4 (×4/N): {method4:.6f}")
    
    print()
    print("与真实幅值的误差:")
    print(f"  方法1误差: {abs(method1 - true_amplitude):.6f} ({abs(method1 - true_amplitude)/true_amplitude*100:.2f}%)")
    print(f"  方法2误差: {abs(method2 - true_amplitude):.6f} ({abs(method2 - true_amplitude)/true_amplitude*100:.2f}%)")
    print(f"  方法3误差: {abs(method3 - true_amplitude):.6f} ({abs(method3 - true_amplitude)/true_amplitude*100:.2f}%)")
    print(f"  方法4误差: {abs(method4 - true_amplitude):.6f} ({abs(method4 - true_amplitude)/true_amplitude*100:.2f}%)")
    
    # 理论分析
    print()
    print("理论分析:")
    print("对于实数信号 x(t) = A*sin(2πft + φ):")
    print("  - 信号的峰值幅度 = A")
    print("  - FFT后，正频率分量的幅度 = A*N/2 (N为采样点数)")
    print("  - 因此，从FFT幅度恢复峰值: A = FFT_magnitude * 2 / N")
    
    theoretical_amplitude = peak_magnitude * 2.0 / samples_num
    print(f"  理论计算结果: {theoretical_amplitude:.6f}")
    print(f"  理论误差: {abs(theoretical_amplitude - true_amplitude):.6f} ({abs(theoretical_amplitude - true_amplitude)/true_amplitude*100:.2f}%)")


def test_multiple_amplitudes():
    """测试多个幅值的FFT转换"""
    print("\n=== 多幅值FFT转换测试 ===")
    
    sampling_rate = 1000
    samples_num = 2000
    frequency = 50.0
    phase = 0.0
    
    test_amplitudes = [0.5, 1.0, 1.414, 2.0, 3.0]
    
    print("测试不同幅值的FFT转换准确性:")
    print("真实幅值 -> FFT幅度 -> 转换结果 -> 误差")
    
    for true_amp in test_amplitudes:
        # 生成信号
        sampling_info = init_sampling_info(sampling_rate, samples_num)
        sine_args = init_sine_args(frequency, true_amp, phase)
        test_wave = get_sine(sampling_info, sine_args)
        
        # 获取数据
        waveform_data = test_wave if test_wave.ndim == 1 else test_wave[0, :]
        
        # FFT分析
        fft_result = np.fft.fft(waveform_data)
        fft_freqs = np.fft.fftfreq(samples_num, 1/sampling_rate)
        
        positive_mask = fft_freqs > 0
        fft_freqs_pos = fft_freqs[positive_mask]
        fft_magnitude = np.abs(fft_result[positive_mask])
        
        # 找峰值
        peak_idx = np.argmin(np.abs(fft_freqs_pos - frequency))
        peak_mag = fft_magnitude[peak_idx]
        
        # 正确的转换方法
        converted_amp = peak_mag * 2.0 / samples_num
        error = abs(converted_amp - true_amp)
        rel_error = error / true_amp * 100
        
        print(f"{true_amp:6.3f} -> {peak_mag:8.3f} -> {converted_amp:6.3f} -> {error:.6f} ({rel_error:.2f}%)")


if __name__ == "__main__":
    print("开始FFT幅值估计调试")
    print("=" * 50)
    
    debug_fft_amplitude()
    test_multiple_amplitudes()
    
    print("\n🔍 FFT幅值调试完成！")
