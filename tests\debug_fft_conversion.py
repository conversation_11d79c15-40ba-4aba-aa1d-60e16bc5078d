"""
调试FFT转换公式

确定正确的FFT幅度谱到实际幅值的转换公式
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
)


def test_fft_conversion_formula():
    """测试FFT转换公式"""
    print("=== FFT转换公式测试 ===")
    
    # 测试多种参数组合
    test_cases = [
        (1000, 2000, 50.0, 1.414),    # FFT调试测试的参数
        (1000, 1000, 100.0, 2.0),     # 简单参数
        (68600, 34300, 3430.8, 1.414), # 你的问题参数
    ]
    
    for fs, N, freq, amp in test_cases:
        print(f"\n--- 测试: fs={fs}Hz, N={N}, f={freq}Hz, A={amp} ---")
        
        # 生成信号
        sampling_info = init_sampling_info(fs, N)
        sine_args = init_sine_args(freq, amp, 0.0)
        test_wave = get_sine(sampling_info, sine_args)
        
        # 获取数据
        data = test_wave if test_wave.ndim == 1 else test_wave[0, :]
        
        # 验证信号的实际幅值
        actual_max = np.max(data)
        actual_min = np.min(data)
        actual_peak_to_peak = actual_max - actual_min
        actual_amplitude = actual_peak_to_peak / 2
        
        print(f"信号验证: 最大值={actual_max:.6f}, 最小值={actual_min:.6f}")
        print(f"实际幅值: {actual_amplitude:.6f} (应该等于{amp})")
        
        # FFT分析
        fft_result = np.fft.fft(data)
        fft_freqs = np.fft.fftfreq(N, 1/fs)
        
        # 找到目标频率的FFT值
        pos_mask = fft_freqs > 0
        fft_freqs_pos = fft_freqs[pos_mask]
        fft_magnitude = np.abs(fft_result[pos_mask])
        
        target_idx = np.argmin(np.abs(fft_freqs_pos - freq))
        fft_peak = fft_magnitude[target_idx]
        
        print(f"FFT峰值: {fft_peak:.6f}")
        
        # 测试不同的转换公式
        method1 = fft_peak * 2.0
        method2 = fft_peak * 2.0 / N
        method3 = fft_peak / N
        method4 = fft_peak
        
        print(f"转换方法:")
        print(f"  方法1 (×2): {method1:.6f}, 误差={abs(method1-amp):.6f}")
        print(f"  方法2 (×2/N): {method2:.6f}, 误差={abs(method2-amp):.6f}")
        print(f"  方法3 (/N): {method3:.6f}, 误差={abs(method3-amp):.6f}")
        print(f"  方法4 (直接): {method4:.6f}, 误差={abs(method4-amp):.6f}")
        
        # 找出最佳方法
        errors = [abs(method1-amp), abs(method2-amp), abs(method3-amp), abs(method4-amp)]
        best_idx = np.argmin(errors)
        methods = ["×2", "×2/N", "/N", "直接"]
        print(f"最佳方法: {methods[best_idx]} (误差最小: {errors[best_idx]:.6f})")


def test_numpy_fft_behavior():
    """测试numpy FFT的具体行为"""
    print("\n=== numpy FFT行为测试 ===")
    
    # 创建一个简单的已知信号
    N = 1000
    fs = 1000
    freq = 100
    amp = 2.0
    
    # 手动创建正弦信号
    t = np.arange(N) / fs
    signal = amp * np.sin(2 * np.pi * freq * t)
    
    print(f"手动信号: N={N}, fs={fs}, f={freq}Hz, A={amp}")
    print(f"信号最大值: {np.max(signal):.6f}")
    print(f"信号最小值: {np.min(signal):.6f}")
    
    # FFT
    fft_result = np.fft.fft(signal)
    fft_freqs = np.fft.fftfreq(N, 1/fs)
    
    # 找到100Hz的分量
    target_idx = np.where(fft_freqs == freq)[0][0]  # 精确匹配
    fft_value = fft_result[target_idx]
    fft_magnitude = abs(fft_value)
    
    print(f"FFT[{freq}Hz]: {fft_value}")
    print(f"FFT幅度: {fft_magnitude:.6f}")
    
    # 理论分析
    print(f"\n理论分析:")
    print(f"对于 x(t) = A*sin(2πft), FFT的理论值应该是:")
    print(f"  正频率分量: -j*A*N/2 = -j*{amp}*{N}/2 = -j*{amp*N/2}")
    print(f"  幅度: {amp*N/2}")
    
    theoretical_magnitude = amp * N / 2
    print(f"理论FFT幅度: {theoretical_magnitude}")
    print(f"实际FFT幅度: {fft_magnitude}")
    print(f"比值: {fft_magnitude / theoretical_magnitude:.6f}")
    
    # 转换回幅值
    recovered_amp = fft_magnitude * 2 / N
    print(f"恢复的幅值: {recovered_amp:.6f}")
    print(f"误差: {abs(recovered_amp - amp):.6f}")


if __name__ == "__main__":
    print("开始FFT转换公式调试")
    print("=" * 50)
    
    test_fft_conversion_formula()
    test_numpy_fft_behavior()
    
    print("\n🔍 FFT转换调试完成！")
