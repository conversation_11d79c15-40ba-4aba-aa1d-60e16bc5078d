"""
comprehensive test for extract_single_tone_information_vvi function

测试 extract_single_tone_information_vvi 函数的全面功能，特别关注：
1. 基本功能验证
2. 不同初始相位的稳定性
3. 边界条件处理
4. 各个运算环节的正确性
"""

import numpy as np
import pytest
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
    extract_single_tone_information_vvi,
)


def test_basic_functionality():
    """测试基本功能是否正常工作"""
    print("\n=== 基本功能测试 ===")
    
    # 创建标准测试信号
    sampling_info = init_sampling_info(1000, 2000)  # 1kHz采样率，2000个点
    sine_args = init_sine_args(50.0, 2.0, 0.0)  # 50Hz，幅值2.0，相位0.0
    
    print(f"生成测试信号: {sine_args}")
    test_wave = get_sine(sampling_info, sine_args)
    print(f"信号形状: {test_wave.shape}")
    
    # 提取信号信息
    detected_args = extract_single_tone_information_vvi(test_wave, approx_freq=50.0)
    
    print(f"检测结果: {detected_args}")
    
    # 验证结果精度
    freq_error = abs(detected_args["frequency"] - sine_args["frequency"])
    amp_error = abs(detected_args["amplitude"] - sine_args["amplitude"])
    phase_error = abs(detected_args["phase"] - sine_args["phase"])
    
    print(f"频率误差: {freq_error:.6f} Hz")
    print(f"幅值误差: {amp_error:.6f}")
    print(f"相位误差: {phase_error:.6f} rad ({phase_error*180/np.pi:.3f}°)")
    
    # 基本精度要求
    assert freq_error < 0.1, f"频率误差过大: {freq_error}"
    assert amp_error < 0.1, f"幅值误差过大: {amp_error}"
    assert phase_error < 0.1, f"相位误差过大: {phase_error}"
    
    print("✓ 基本功能测试通过")


def test_phase_stability():
    """测试不同初始相位下的稳定性"""
    print("\n=== 相位稳定性测试 ===")
    
    sampling_info = init_sampling_info(1000, 3000)  # 1kHz采样率，3000个点
    frequency = 50.0
    amplitude = 1.5
    
    # 测试多个相位值，包括边界情况
    test_phases = [
        -np.pi,      # 下边界
        -np.pi + 0.1,  # 接近下边界
        -np.pi/2,    # 负π/2
        -0.1,        # 接近0的负值
        0.0,         # 零相位
        0.1,         # 接近0的正值
        np.pi/2,     # 正π/2
        np.pi - 0.1, # 接近上边界
        np.pi,       # 上边界
    ]
    
    results = []
    
    for i, phase in enumerate(test_phases):
        print(f"\n--- 测试相位 {i+1}/{len(test_phases)}: {phase:.3f} rad ({phase*180/np.pi:.1f}°) ---")
        
        # 生成测试信号
        sine_args = init_sine_args(frequency, amplitude, phase)
        test_wave = get_sine(sampling_info, sine_args)
        
        try:
            # 提取信号信息
            detected_args = extract_single_tone_information_vvi(test_wave, approx_freq=frequency)
            
            # 计算误差
            freq_error = abs(detected_args["frequency"] - frequency)
            amp_error = abs(detected_args["amplitude"] - amplitude)
            
            # 相位误差需要考虑周期性
            phase_diff = detected_args["phase"] - phase
            # 将相位差归一化到 [-π, π] 范围
            phase_error = np.abs(np.arctan2(np.sin(phase_diff), np.cos(phase_diff)))
            
            results.append({
                "original_phase": phase,
                "detected_phase": detected_args["phase"],
                "freq_error": freq_error,
                "amp_error": amp_error,
                "phase_error": phase_error,
                "success": True
            })
            
            print(f"原始相位: {phase:.3f} rad ({phase*180/np.pi:.1f}°)")
            print(f"检测相位: {detected_args['phase']:.3f} rad ({detected_args['phase']*180/np.pi:.1f}°)")
            print(f"相位误差: {phase_error:.3f} rad ({phase_error*180/np.pi:.1f}°)")
            print(f"频率误差: {freq_error:.6f} Hz")
            print(f"幅值误差: {amp_error:.6f}")
            
            # 检查是否在可接受范围内
            if phase_error > 0.2:  # 相位误差超过0.2弧度(约11.5度)
                print(f"⚠️  相位误差较大: {phase_error:.3f} rad")
            if freq_error > 0.1:
                print(f"⚠️  频率误差较大: {freq_error:.6f} Hz")
            if amp_error > 0.1:
                print(f"⚠️  幅值误差较大: {amp_error:.6f}")
                
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            results.append({
                "original_phase": phase,
                "detected_phase": np.nan,
                "freq_error": np.inf,
                "amp_error": np.inf,
                "phase_error": np.inf,
                "success": False,
                "error": str(e)
            })
    
    # 统计结果
    successful_tests = sum(1 for r in results if r["success"])
    print(f"\n=== 相位稳定性测试总结 ===")
    print(f"成功测试: {successful_tests}/{len(test_phases)}")
    
    if successful_tests > 0:
        successful_results = [r for r in results if r["success"]]
        avg_phase_error = np.mean([r["phase_error"] for r in successful_results])
        max_phase_error = np.max([r["phase_error"] for r in successful_results])
        avg_freq_error = np.mean([r["freq_error"] for r in successful_results])
        avg_amp_error = np.mean([r["amp_error"] for r in successful_results])
        
        print(f"平均相位误差: {avg_phase_error:.3f} rad ({avg_phase_error*180/np.pi:.1f}°)")
        print(f"最大相位误差: {max_phase_error:.3f} rad ({max_phase_error*180/np.pi:.1f}°)")
        print(f"平均频率误差: {avg_freq_error:.6f} Hz")
        print(f"平均幅值误差: {avg_amp_error:.6f}")
    
    # 检查失败的测试
    failed_tests = [r for r in results if not r["success"]]
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for r in failed_tests:
            print(f"  相位 {r['original_phase']:.3f} rad: {r.get('error', 'Unknown error')}")
    
    return results


def test_edge_cases():
    """测试边界条件和特殊情况"""
    print("\n=== 边界条件测试 ===")
    
    # 测试1: 很短的信号
    print("\n--- 测试短信号 ---")
    sampling_info_short = init_sampling_info(1000, 100)  # 只有100个点
    sine_args = init_sine_args(50.0, 1.0, 0.5)
    test_wave_short = get_sine(sampling_info_short, sine_args)
    
    try:
        detected_args = extract_single_tone_information_vvi(test_wave_short, approx_freq=50.0)
        print(f"短信号检测成功: {detected_args}")
    except Exception as e:
        print(f"短信号检测失败: {e}")
    
    # 测试2: 很长的信号
    print("\n--- 测试长信号 ---")
    sampling_info_long = init_sampling_info(1000, 10000)  # 10000个点
    test_wave_long = get_sine(sampling_info_long, sine_args)
    
    try:
        detected_args = extract_single_tone_information_vvi(test_wave_long, approx_freq=50.0)
        print(f"长信号检测成功: {detected_args}")
    except Exception as e:
        print(f"长信号检测失败: {e}")
    
    # 测试3: 高频信号
    print("\n--- 测试高频信号 ---")
    sampling_info_hf = init_sampling_info(10000, 5000)  # 10kHz采样率
    sine_args_hf = init_sine_args(2000.0, 1.0, 1.0)  # 2kHz信号
    test_wave_hf = get_sine(sampling_info_hf, sine_args_hf)
    
    try:
        detected_args = extract_single_tone_information_vvi(test_wave_hf, approx_freq=2000.0)
        print(f"高频信号检测成功: {detected_args}")
    except Exception as e:
        print(f"高频信号检测失败: {e}")


if __name__ == "__main__":
    print("开始 extract_single_tone_information_vvi 函数的全面测试")
    
    try:
        test_basic_functionality()
        phase_results = test_phase_stability()
        test_edge_cases()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
