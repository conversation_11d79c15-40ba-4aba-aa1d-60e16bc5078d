"""
调试你的具体测试案例

检查在你的参数下FFT的具体行为
"""

import numpy as np
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine,
)


def debug_your_specific_case():
    """调试你的具体测试案例"""
    print("=== 调试你的具体测试案例 ===")
    
    # 你的测试参数
    sampling_info = init_sampling_info(68600, 34300)
    sine_args = init_sine_args(3430.8, 1.414, 1.73)
    
    print(f"测试参数:")
    print(f"  采样率: {sampling_info['sampling_rate']} Hz")
    print(f"  采样点数: {sampling_info['samples_num']}")
    print(f"  频率: {sine_args['frequency']} Hz")
    print(f"  真实幅值: {sine_args['amplitude']}")
    print(f"  相位: {sine_args['phase']} rad")
    print()
    
    # 生成信号
    testwave = get_sine(sampling_info, sine_args)
    
    # 获取信号数据
    if testwave.ndim == 2:
        waveform_data = testwave[0, :]
    else:
        waveform_data = testwave
    
    samples_num = len(waveform_data)
    sampling_rate = sampling_info['sampling_rate']
    
    print(f"信号统计:")
    print(f"  最大值: {np.max(waveform_data):.6f}")
    print(f"  最小值: {np.min(waveform_data):.6f}")
    print(f"  峰峰值: {np.max(waveform_data) - np.min(waveform_data):.6f}")
    print(f"  RMS值: {np.sqrt(np.mean(waveform_data**2)):.6f}")
    print()
    
    # 进行FFT分析
    fft_result = np.fft.fft(waveform_data)
    fft_freqs = np.fft.fftfreq(samples_num, 1/sampling_rate)
    
    # 只取正频率部分
    positive_freq_mask = fft_freqs > 0
    fft_freqs_positive = fft_freqs[positive_freq_mask]
    fft_magnitude = np.abs(fft_result[positive_freq_mask])
    
    # 找到目标频率附近的峰值
    target_freq = sine_args['frequency']
    target_freq_idx = np.argmin(np.abs(fft_freqs_positive - target_freq))
    peak_magnitude = fft_magnitude[target_freq_idx]
    peak_frequency = fft_freqs_positive[target_freq_idx]
    
    print(f"FFT分析结果:")
    print(f"  峰值频率: {peak_frequency:.6f} Hz")
    print(f"  FFT幅度谱值: {peak_magnitude:.6f}")
    print()
    
    # 测试不同的幅值转换方法
    print("不同幅值转换方法:")
    
    # 方法1: 直接乘以2（原始错误方法）
    method1 = peak_magnitude * 2.0
    print(f"  方法1 (×2): {method1:.6f}")
    
    # 方法2: 正确的FFT转换
    method2 = peak_magnitude * 2.0 / samples_num
    print(f"  方法2 (×2/N): {method2:.6f}")
    
    # 方法3: 检查是否FFT已经归一化
    method3 = peak_magnitude
    print(f"  方法3 (直接使用): {method3:.6f}")
    
    # 方法4: 检查numpy FFT的具体行为
    # numpy.fft.fft 不进行归一化，所以需要除以N
    method4 = peak_magnitude / samples_num
    print(f"  方法4 (/N): {method4:.6f}")
    
    print()
    print("与真实幅值的误差:")
    true_amp = sine_args['amplitude']
    print(f"  方法1误差: {abs(method1 - true_amp):.6f} ({abs(method1 - true_amp)/true_amp*100:.2f}%)")
    print(f"  方法2误差: {abs(method2 - true_amp):.6f} ({abs(method2 - true_amp)/true_amp*100:.2f}%)")
    print(f"  方法3误差: {abs(method3 - true_amp):.6f} ({abs(method3 - true_amp)/true_amp*100:.2f}%)")
    print(f"  方法4误差: {abs(method4 - true_amp):.6f} ({abs(method4 - true_amp)/true_amp*100:.2f}%)")
    
    # 找出最佳方法
    errors = [
        abs(method1 - true_amp),
        abs(method2 - true_amp), 
        abs(method3 - true_amp),
        abs(method4 - true_amp)
    ]
    best_method = np.argmin(errors) + 1
    print(f"\n最佳方法: 方法{best_method} (误差最小)")
    
    # 检查频率分辨率
    freq_resolution = sampling_rate / samples_num
    print(f"\n频率分辨率: {freq_resolution:.6f} Hz")
    print(f"目标频率与最近FFT频率的差异: {abs(peak_frequency - target_freq):.6f} Hz")


def compare_fft_implementations():
    """比较不同参数下的FFT行为"""
    print("\n=== 比较不同参数下的FFT行为 ===")
    
    test_cases = [
        # (采样率, 采样点数, 频率, 幅值, 名称)
        (1000, 2000, 50.0, 1.414, "FFT调试测试参数"),
        (68600, 34300, 3430.8, 1.414, "你的测试参数"),
        (1000, 1000, 100.0, 2.0, "简单测试参数"),
    ]
    
    for fs, N, freq, amp, name in test_cases:
        print(f"\n--- {name} ---")
        print(f"参数: fs={fs}Hz, N={N}, f={freq}Hz, A={amp}")
        
        # 生成信号
        sampling_info = init_sampling_info(fs, N)
        sine_args = init_sine_args(freq, amp, 0.0)
        test_wave = get_sine(sampling_info, sine_args)
        
        # 获取数据
        data = test_wave if test_wave.ndim == 1 else test_wave[0, :]
        
        # FFT
        fft_result = np.fft.fft(data)
        fft_freqs = np.fft.fftfreq(N, 1/fs)
        
        # 找峰值
        pos_mask = fft_freqs > 0
        fft_freqs_pos = fft_freqs[pos_mask]
        fft_mag = np.abs(fft_result[pos_mask])
        
        peak_idx = np.argmin(np.abs(fft_freqs_pos - freq))
        peak_mag = fft_mag[peak_idx]
        
        # 测试转换
        converted_amp = peak_mag * 2.0 / N
        error = abs(converted_amp - amp)
        rel_error = error / amp * 100
        
        print(f"FFT峰值: {peak_mag:.6f}")
        print(f"转换结果: {converted_amp:.6f}")
        print(f"误差: {error:.6f} ({rel_error:.2f}%)")


if __name__ == "__main__":
    print("开始调试你的具体测试案例")
    print("=" * 50)
    
    debug_your_specific_case()
    compare_fft_implementations()
    
    print("\n🔍 调试完成！")
